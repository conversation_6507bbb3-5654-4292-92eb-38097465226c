package com.example.monster;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 妖怪生平录应用程序启动类
 */
@SpringBootApplication
public class MonsterApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(MonsterApplication.class, args);
        System.out.println("=================================");
        System.out.println("妖怪生平录系统启动成功！");
        System.out.println("访问地址：http://localhost:8080");
        System.out.println("=================================");
    }
}
