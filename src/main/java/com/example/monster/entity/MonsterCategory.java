package com.example.monster.entity;

import javax.persistence.*;
import java.util.List;

/**
 * 妖怪分类实体类
 */
@Entity
@Table(name = "monster_categories")
public class MonsterCategory {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    
    @Column(name = "name", nullable = false, length = 50)
    private String name;
    
    @OneToMany(mappedBy = "category", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Monster> monsters;
    
    // 构造函数
    public MonsterCategory() {}
    
    public MonsterCategory(String name) {
        this.name = name;
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public List<Monster> getMonsters() {
        return monsters;
    }
    
    public void setMonsters(List<Monster> monsters) {
        this.monsters = monsters;
    }
    
    @Override
    public String toString() {
        return "MonsterCategory{" +
                "id=" + id +
                ", name='" + name + '\'' +
                '}';
    }
}
