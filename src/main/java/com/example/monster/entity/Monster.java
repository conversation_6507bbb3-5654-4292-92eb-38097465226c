package com.example.monster.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;

/**
 * 妖怪实体类
 */
@Entity
@Table(name = "monsters")
public class Monster {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    
    @Column(name = "name", nullable = false, length = 100)
    private String name;
    
    @Column(name = "poem", columnDefinition = "TEXT")
    private String poem;
    
    @Column(name = "story", columnDefinition = "TEXT")
    private String story;
    
    @Column(name = "image_path", length = 255)
    private String imagePath;
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "category_id", nullable = false)
    private MonsterCategory category;
    
    // 构造函数
    public Monster() {}
    
    public Monster(String name, String poem, String story, String imagePath, MonsterCategory category) {
        this.name = name;
        this.poem = poem;
        this.story = story;
        this.imagePath = imagePath;
        this.category = category;
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getPoem() {
        return poem;
    }
    
    public void setPoem(String poem) {
        this.poem = poem;
    }
    
    public String getStory() {
        return story;
    }
    
    public void setStory(String story) {
        this.story = story;
    }
    
    public String getImagePath() {
        return imagePath;
    }
    
    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }
    
    public MonsterCategory getCategory() {
        return category;
    }
    
    public void setCategory(MonsterCategory category) {
        this.category = category;
    }
    
    @Override
    public String toString() {
        return "Monster{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", poem='" + poem + '\'' +
                ", story='" + story + '\'' +
                ", imagePath='" + imagePath + '\'' +
                ", category=" + (category != null ? category.getName() : null) +
                '}';
    }
}
