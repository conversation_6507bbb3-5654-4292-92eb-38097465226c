package com.example.monster.controller;

import com.example.monster.entity.Monster;
import com.example.monster.entity.MonsterCategory;
import com.example.monster.service.MonsterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 妖怪控制器
 */
@RestController
@RequestMapping("/api/monsters")
@CrossOrigin(origins = "*")
public class MonsterController {
    
    @Autowired
    private MonsterService monsterService;
    
    /**
     * 获取所有妖怪分类
     * @return 分类列表
     */
    @GetMapping("/categories")
    public ResponseEntity<List<MonsterCategory>> getAllCategories() {
        List<MonsterCategory> categories = monsterService.getAllCategories();
        return ResponseEntity.ok(categories);
    }
    
    /**
     * 获取所有妖怪
     * @return 妖怪列表
     */
    @GetMapping
    public ResponseEntity<List<Monster>> getAllMonsters() {
        List<Monster> monsters = monsterService.getAllMonsters();
        return ResponseEntity.ok(monsters);
    }
    
    /**
     * 分页获取妖怪列表
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 分页结果
     */
    @GetMapping("/page")
    public ResponseEntity<Page<Monster>> getMonstersWithPagination(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<Monster> monsters = monsterService.getMonsters(page, size);
        return ResponseEntity.ok(monsters);
    }
    
    /**
     * 根据ID获取妖怪详情
     * @param id 妖怪ID
     * @return 妖怪详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Monster> getMonsterById(@PathVariable Integer id) {
        Optional<Monster> monster = monsterService.getMonsterById(id);
        if (monster.isPresent()) {
            return ResponseEntity.ok(monster.get());
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * 搜索妖怪
     * @param categoryId 分类ID（可选）
     * @param name 妖怪名称（可选）
     * @return 搜索结果
     */
    @GetMapping("/search")
    public ResponseEntity<List<Monster>> searchMonsters(
            @RequestParam(required = false) Integer categoryId,
            @RequestParam(required = false) String name) {
        List<Monster> monsters = monsterService.searchMonsters(categoryId, name);
        return ResponseEntity.ok(monsters);
    }
    
    /**
     * 分页搜索妖怪
     * @param categoryId 分类ID（可选）
     * @param name 妖怪名称（可选）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 分页搜索结果
     */
    @GetMapping("/search/page")
    public ResponseEntity<Page<Monster>> searchMonstersWithPagination(
            @RequestParam(required = false) Integer categoryId,
            @RequestParam(required = false) String name,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<Monster> monsters = monsterService.searchMonstersWithPagination(categoryId, name, page, size);
        return ResponseEntity.ok(monsters);
    }
}
