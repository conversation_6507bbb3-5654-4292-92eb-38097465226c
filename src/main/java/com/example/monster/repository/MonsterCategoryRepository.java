package com.example.monster.repository;

import com.example.monster.entity.MonsterCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * 妖怪分类数据访问接口
 */
@Repository
public interface MonsterCategoryRepository extends JpaRepository<MonsterCategory, Integer> {
    
    /**
     * 根据分类名称查找分类
     * @param name 分类名称
     * @return 分类对象
     */
    MonsterCategory findByName(String name);
}
