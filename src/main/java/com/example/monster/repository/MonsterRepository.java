package com.example.monster.repository;

import com.example.monster.entity.Monster;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 妖怪数据访问接口
 */
@Repository
public interface MonsterRepository extends JpaRepository<Monster, Integer> {
    
    /**
     * 根据妖怪名称模糊查询
     * @param name 妖怪名称
     * @return 妖怪列表
     */
    List<Monster> findByNameContaining(String name);
    
    /**
     * 根据分类ID查询妖怪
     * @param categoryId 分类ID
     * @return 妖怪列表
     */
    List<Monster> findByCategoryId(Integer categoryId);
    
    /**
     * 根据分类ID和名称模糊查询妖怪
     * @param categoryId 分类ID
     * @param name 妖怪名称
     * @return 妖怪列表
     */
    List<Monster> findByCategoryIdAndNameContaining(Integer categoryId, String name);
    
    /**
     * 分页查询所有妖怪
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<Monster> findAll(Pageable pageable);
    
    /**
     * 根据分类ID分页查询妖怪
     * @param categoryId 分类ID
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<Monster> findByCategoryId(Integer categoryId, Pageable pageable);
    
    /**
     * 根据名称模糊查询并分页
     * @param name 妖怪名称
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<Monster> findByNameContaining(String name, Pageable pageable);
    
    /**
     * 根据分类ID和名称模糊查询并分页
     * @param categoryId 分类ID
     * @param name 妖怪名称
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<Monster> findByCategoryIdAndNameContaining(Integer categoryId, String name, Pageable pageable);
}
