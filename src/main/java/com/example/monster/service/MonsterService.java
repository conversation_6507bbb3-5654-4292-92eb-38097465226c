package com.example.monster.service;

import com.example.monster.entity.Monster;
import com.example.monster.entity.MonsterCategory;
import com.example.monster.repository.MonsterCategoryRepository;
import com.example.monster.repository.MonsterRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 妖怪业务逻辑服务类
 */
@Service
public class MonsterService {
    
    @Autowired
    private MonsterRepository monsterRepository;
    
    @Autowired
    private MonsterCategoryRepository categoryRepository;
    
    /**
     * 获取所有妖怪分类
     * @return 分类列表
     */
    public List<MonsterCategory> getAllCategories() {
        return categoryRepository.findAll();
    }
    
    /**
     * 获取所有妖怪
     * @return 妖怪列表
     */
    public List<Monster> getAllMonsters() {
        return monsterRepository.findAll(Sort.by(Sort.Direction.ASC, "id"));
    }
    
    /**
     * 分页获取妖怪列表
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 分页结果
     */
    public Page<Monster> getMonsters(int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "id"));
        return monsterRepository.findAll(pageable);
    }
    
    /**
     * 根据ID获取妖怪详情
     * @param id 妖怪ID
     * @return 妖怪对象
     */
    public Optional<Monster> getMonsterById(Integer id) {
        return monsterRepository.findById(id);
    }
    
    /**
     * 根据名称搜索妖怪
     * @param name 妖怪名称
     * @return 妖怪列表
     */
    public List<Monster> searchMonstersByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return getAllMonsters();
        }
        return monsterRepository.findByNameContaining(name.trim());
    }
    
    /**
     * 根据分类筛选妖怪
     * @param categoryId 分类ID
     * @return 妖怪列表
     */
    public List<Monster> getMonstersByCategory(Integer categoryId) {
        if (categoryId == null) {
            return getAllMonsters();
        }
        return monsterRepository.findByCategoryId(categoryId);
    }
    
    /**
     * 根据分类和名称搜索妖怪
     * @param categoryId 分类ID
     * @param name 妖怪名称
     * @return 妖怪列表
     */
    public List<Monster> searchMonsters(Integer categoryId, String name) {
        // 如果分类和名称都为空，返回所有妖怪
        if ((categoryId == null) && (name == null || name.trim().isEmpty())) {
            return getAllMonsters();
        }
        
        // 只有分类条件
        if (categoryId != null && (name == null || name.trim().isEmpty())) {
            return monsterRepository.findByCategoryId(categoryId);
        }
        
        // 只有名称条件
        if (categoryId == null && name != null && !name.trim().isEmpty()) {
            return monsterRepository.findByNameContaining(name.trim());
        }
        
        // 同时有分类和名称条件
        return monsterRepository.findByCategoryIdAndNameContaining(categoryId, name.trim());
    }
    
    /**
     * 分页搜索妖怪
     * @param categoryId 分类ID
     * @param name 妖怪名称
     * @param page 页码
     * @param size 每页大小
     * @return 分页结果
     */
    public Page<Monster> searchMonstersWithPagination(Integer categoryId, String name, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "id"));
        
        // 如果分类和名称都为空，返回所有妖怪
        if ((categoryId == null) && (name == null || name.trim().isEmpty())) {
            return monsterRepository.findAll(pageable);
        }
        
        // 只有分类条件
        if (categoryId != null && (name == null || name.trim().isEmpty())) {
            return monsterRepository.findByCategoryId(categoryId, pageable);
        }
        
        // 只有名称条件
        if (categoryId == null && name != null && !name.trim().isEmpty()) {
            return monsterRepository.findByNameContaining(name.trim(), pageable);
        }
        
        // 同时有分类和名称条件
        return monsterRepository.findByCategoryIdAndNameContaining(categoryId, name.trim(), pageable);
    }
}
