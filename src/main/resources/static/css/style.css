/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 页面头部 */
.header {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2rem 0;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.title {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-style: italic;
}

/* 搜索区域 */
.search-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem 0;
    margin: 2rem 0;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: end;
    justify-content: center;
}

.form-group {
    display: flex;
    flex-direction: column;
    min-width: 200px;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #555;
}

.form-control {
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* 妖怪列表区域 */
.monsters-section {
    background: rgba(255, 255, 255, 0.95);
    margin: 2rem 0;
    padding: 2rem 0;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.monsters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #eee;
}

.monsters-header h2 {
    color: #333;
    font-size: 1.8rem;
}

.monsters-count {
    color: #666;
    font-size: 1rem;
}

.monsters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* 妖怪卡片 */
.monster-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.monster-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.monster-card-image {
    width: 100%;
    height: 200px;
    background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 0.9rem;
}

.monster-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.monster-card-content {
    padding: 1.5rem;
}

.monster-card-title {
    font-size: 1.3rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
}

.monster-card-category {
    color: #667eea;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.monster-card-poem {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    font-style: italic;
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 加载和无数据提示 */
.loading, .no-data {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 10px 10px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
}

.close {
    color: white;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    opacity: 0.7;
}

.modal-body {
    padding: 2rem;
}

.monster-detail {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
}

.monster-image img {
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.monster-info h4 {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 1rem;
}

.monster-category {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.monster-category strong {
    color: #667eea;
}

.monster-poem, .monster-story {
    margin-bottom: 2rem;
}

.monster-poem h5, .monster-story h5 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.poem-content {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 5px;
    font-style: italic;
    line-height: 1.8;
    white-space: pre-line;
}

.story-content {
    line-height: 1.8;
    text-align: justify;
    white-space: pre-line;
}

/* 页脚 */
.footer {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 2rem;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .title {
        font-size: 2rem;
    }
    
    .search-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .form-group {
        min-width: auto;
    }
    
    .monsters-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .monsters-grid {
        grid-template-columns: 1fr;
    }
    
    .monster-detail {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 5% auto;
    }
}
