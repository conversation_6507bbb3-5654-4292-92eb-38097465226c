$(document).ready(function() {
    // 全局变量
    let allMonsters = [];
    let allCategories = [];
    
    // API基础URL
    const API_BASE_URL = '/api/monsters';
    
    // 初始化应用
    init();
    
    function init() {
        loadCategories();
        loadMonsters();
        bindEvents();
    }
    
    // 绑定事件
    function bindEvents() {
        // 搜索按钮点击事件
        $('#searchBtn').click(function() {
            searchMonsters();
        });
        
        // 重置按钮点击事件
        $('#resetBtn').click(function() {
            resetSearch();
        });
        
        // 输入框回车事件
        $('#nameInput').keypress(function(e) {
            if (e.which === 13) {
                searchMonsters();
            }
        });
        
        // 分类选择变化事件
        $('#categorySelect').change(function() {
            searchMonsters();
        });
        
        // 模态框关闭事件
        $('.close').click(function() {
            closeModal();
        });
        
        // 点击模态框外部关闭
        $(window).click(function(event) {
            if (event.target.id === 'monsterModal') {
                closeModal();
            }
        });
    }
    
    // 加载分类数据
    function loadCategories() {
        $.ajax({
            url: API_BASE_URL + '/categories',
            method: 'GET',
            success: function(data) {
                allCategories = data;
                renderCategories();
            },
            error: function(xhr, status, error) {
                console.error('加载分类失败:', error);
                showError('加载分类数据失败，请刷新页面重试');
            }
        });
    }
    
    // 渲染分类选择框
    function renderCategories() {
        const categorySelect = $('#categorySelect');
        categorySelect.empty();
        categorySelect.append('<option value="">全部分类</option>');
        
        allCategories.forEach(function(category) {
            categorySelect.append(`<option value="${category.id}">${category.name}</option>`);
        });
    }
    
    // 加载妖怪数据
    function loadMonsters() {
        showLoading();
        
        $.ajax({
            url: API_BASE_URL,
            method: 'GET',
            success: function(data) {
                allMonsters = data;
                renderMonsters(data);
                updateCount(data.length);
                hideLoading();
            },
            error: function(xhr, status, error) {
                console.error('加载妖怪数据失败:', error);
                hideLoading();
                showError('加载妖怪数据失败，请刷新页面重试');
            }
        });
    }
    
    // 搜索妖怪
    function searchMonsters() {
        const categoryId = $('#categorySelect').val();
        const name = $('#nameInput').val().trim();
        
        showLoading();
        
        const params = {};
        if (categoryId) params.categoryId = categoryId;
        if (name) params.name = name;
        
        $.ajax({
            url: API_BASE_URL + '/search',
            method: 'GET',
            data: params,
            success: function(data) {
                renderMonsters(data);
                updateCount(data.length);
                hideLoading();
            },
            error: function(xhr, status, error) {
                console.error('搜索失败:', error);
                hideLoading();
                showError('搜索失败，请重试');
            }
        });
    }
    
    // 重置搜索
    function resetSearch() {
        $('#categorySelect').val('');
        $('#nameInput').val('');
        renderMonsters(allMonsters);
        updateCount(allMonsters.length);
    }
    
    // 渲染妖怪列表
    function renderMonsters(monsters) {
        const monstersGrid = $('#monstersGrid');
        monstersGrid.empty();
        
        if (monsters.length === 0) {
            showNoData();
            return;
        }
        
        hideNoData();
        
        monsters.forEach(function(monster) {
            const monsterCard = createMonsterCard(monster);
            monstersGrid.append(monsterCard);
        });
    }
    
    // 创建妖怪卡片
    function createMonsterCard(monster) {
        const categoryName = monster.category ? monster.category.name : '未分类';
        const poem = monster.poem ? monster.poem.substring(0, 100) + '...' : '暂无诗句';
        const imageSrc = monster.imagePath ? monster.imagePath : '';
        
        const card = $(`
            <div class="monster-card" data-monster-id="${monster.id}">
                <div class="monster-card-image">
                    ${imageSrc ? `<img src="${imageSrc}" alt="${monster.name}" onerror="this.style.display='none'; this.parentNode.innerHTML='暂无图片';">` : '暂无图片'}
                </div>
                <div class="monster-card-content">
                    <div class="monster-card-title">${monster.name}</div>
                    <div class="monster-card-category">${categoryName}</div>
                    <div class="monster-card-poem">${poem}</div>
                </div>
            </div>
        `);
        
        // 绑定点击事件
        card.click(function() {
            showMonsterDetail(monster.id);
        });
        
        return card;
    }
    
    // 显示妖怪详情
    function showMonsterDetail(monsterId) {
        $.ajax({
            url: API_BASE_URL + '/' + monsterId,
            method: 'GET',
            success: function(monster) {
                renderMonsterDetail(monster);
                openModal();
            },
            error: function(xhr, status, error) {
                console.error('加载妖怪详情失败:', error);
                showError('加载妖怪详情失败，请重试');
            }
        });
    }
    
    // 渲染妖怪详情
    function renderMonsterDetail(monster) {
        $('#modalTitle').text('妖怪详情');
        $('#modalName').text(monster.name);
        $('#modalCategory').text(monster.category ? monster.category.name : '未分类');
        $('#modalPoem').text(monster.poem || '暂无诗句');
        $('#modalStory').text(monster.story || '暂无故事');
        
        const modalImage = $('#modalImage');
        if (monster.imagePath) {
            modalImage.attr('src', monster.imagePath);
            modalImage.attr('alt', monster.name);
            modalImage.show();
        } else {
            modalImage.hide();
        }
    }
    
    // 打开模态框
    function openModal() {
        $('#monsterModal').fadeIn(300);
        $('body').css('overflow', 'hidden');
    }
    
    // 关闭模态框
    function closeModal() {
        $('#monsterModal').fadeOut(300);
        $('body').css('overflow', 'auto');
    }
    
    // 更新计数
    function updateCount(count) {
        $('#totalCount').text(count);
    }
    
    // 显示加载状态
    function showLoading() {
        $('#loadingIndicator').show();
        $('#monstersGrid').hide();
        $('#noDataIndicator').hide();
    }
    
    // 隐藏加载状态
    function hideLoading() {
        $('#loadingIndicator').hide();
        $('#monstersGrid').show();
    }
    
    // 显示无数据提示
    function showNoData() {
        $('#noDataIndicator').show();
        $('#monstersGrid').hide();
    }
    
    // 隐藏无数据提示
    function hideNoData() {
        $('#noDataIndicator').hide();
        $('#monstersGrid').show();
    }
    
    // 显示错误信息
    function showError(message) {
        alert('错误: ' + message);
    }
});
