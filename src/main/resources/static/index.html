<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>黑神话悟空 - 妖怪生平录</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header">
        <div class="container">
            <h1 class="title">黑神话悟空 - 妖怪生平录</h1>
            <p class="subtitle">203个妖怪，203首小诗，203个妖生故事</p>
        </div>
    </header>

    <!-- 搜索和筛选区域 -->
    <section class="search-section">
        <div class="container">
            <div class="search-form">
                <div class="form-group">
                    <label for="categorySelect">妖怪分类：</label>
                    <select id="categorySelect" class="form-control">
                        <option value="">全部分类</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="nameInput">妖怪名称：</label>
                    <input type="text" id="nameInput" class="form-control" placeholder="请输入妖怪名称">
                </div>
                <div class="form-group">
                    <button id="searchBtn" class="btn btn-primary">搜索</button>
                    <button id="resetBtn" class="btn btn-secondary">重置</button>
                </div>
            </div>
        </div>
    </section>

    <!-- 妖怪列表区域 -->
    <section class="monsters-section">
        <div class="container">
            <div class="monsters-header">
                <h2>妖怪列表</h2>
                <div class="monsters-count">
                    共找到 <span id="totalCount">0</span> 个妖怪
                </div>
            </div>
            <div id="monstersGrid" class="monsters-grid">
                <!-- 妖怪卡片将通过JavaScript动态生成 -->
            </div>
            
            <!-- 加载提示 -->
            <div id="loadingIndicator" class="loading">
                <div class="loading-spinner"></div>
                <p>正在加载妖怪数据...</p>
            </div>
            
            <!-- 无数据提示 -->
            <div id="noDataIndicator" class="no-data" style="display: none;">
                <p>暂无妖怪数据</p>
            </div>
        </div>
    </section>

    <!-- 妖怪详情模态框 -->
    <div id="monsterModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">妖怪详情</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="monster-detail">
                    <div class="monster-image">
                        <img id="modalImage" src="" alt="妖怪图片">
                    </div>
                    <div class="monster-info">
                        <h4 id="modalName">妖怪名称</h4>
                        <div class="monster-category">
                            <strong>分类：</strong><span id="modalCategory"></span>
                        </div>
                        <div class="monster-poem">
                            <h5>相关诗句：</h5>
                            <div id="modalPoem" class="poem-content"></div>
                        </div>
                        <div class="monster-story">
                            <h5>故事描述：</h5>
                            <div id="modalStory" class="story-content"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 黑神话悟空妖怪生平录. 热爱胜过所有.</p>
        </div>
    </footer>

    <script src="js/app.js"></script>
</body>
</html>
