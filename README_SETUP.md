# 黑神话悟空妖怪生平录系统

基于Spring Boot + jQuery开发的妖怪生平录展示系统，支持查询和类别筛选功能。

## 功能特性

- 🔍 **智能搜索**：支持按妖怪名称模糊搜索
- 📂 **分类筛选**：支持按妖怪类别筛选
- 📱 **响应式设计**：适配PC和移动端
- 🎨 **精美界面**：现代化UI设计，用户体验友好
- 📖 **详情展示**：完整展示妖怪的诗句、故事和图片

## 技术栈

### 后端
- Spring Boot 2.7.0
- Spring Data JPA
- MySQL 8.0
- Maven

### 前端
- HTML5 + CSS3
- jQuery 3.6.0
- 响应式布局

## 项目结构

```
monster-biography/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/example/monster/
│   │   │       ├── entity/          # 实体类
│   │   │       ├── repository/      # 数据访问层
│   │   │       ├── service/         # 业务逻辑层
│   │   │       ├── controller/      # 控制器层
│   │   │       └── MonsterApplication.java
│   │   └── resources/
│   │       ├── static/              # 静态资源
│   │       │   ├── css/
│   │       │   ├── js/
│   │       │   └── index.html
│   │       └── application.yml      # 配置文件
├── create_monsters_database.sql     # 数据库初始化脚本
└── pom.xml                         # Maven配置
```

## 快速开始

### 1. 环境要求

- JDK 8 或更高版本
- Maven 3.6+
- MySQL 8.0+

### 2. 数据库配置

1. 创建数据库：
```sql
CREATE DATABASE IF NOT EXISTS black_myth_wukong;
```

2. 执行数据库初始化脚本：
```bash
mysql -u root -p black_myth_wukong < create_monsters_database.sql
```

3. 修改数据库连接配置（如需要）：
编辑 `src/main/resources/application.yml` 文件中的数据库连接信息：
```yaml
spring:
  datasource:
    url: ******************************************************************************************************************************
    username: root
    password: your_password
```

### 3. 运行项目

1. 使用Maven编译项目：
```bash
mvn clean compile
```

2. 启动应用：
```bash
mvn spring-boot:run
```

或者：

1. 打包项目：
```bash
mvn clean package
```

2. 运行JAR文件：
```bash
java -jar target/monster-biography-1.0.0.jar
```

### 4. 访问应用

启动成功后，在浏览器中访问：
```
http://localhost:8080
```

## API接口

### 妖怪相关接口

- `GET /api/monsters` - 获取所有妖怪
- `GET /api/monsters/{id}` - 根据ID获取妖怪详情
- `GET /api/monsters/search` - 搜索妖怪
  - 参数：`categoryId`（可选）- 分类ID
  - 参数：`name`（可选）- 妖怪名称

### 分类相关接口

- `GET /api/monsters/categories` - 获取所有分类

## 使用说明

1. **浏览妖怪**：打开首页即可看到所有妖怪的卡片展示
2. **分类筛选**：使用顶部的分类下拉框选择特定分类
3. **名称搜索**：在搜索框中输入妖怪名称进行模糊搜索
4. **查看详情**：点击任意妖怪卡片查看详细的诗句和故事
5. **重置搜索**：点击重置按钮清空所有搜索条件

## 数据说明

系统包含以下数据：

### 妖怪分类
- 小妖
- 大妖

### 妖怪信息
每个妖怪包含：
- 名称
- 所属分类
- 相关诗句
- 故事描述
- 图片路径（如有）

## 开发说明

### 添加新妖怪

可以通过以下SQL语句添加新的妖怪数据：

```sql
INSERT INTO monsters (category_id, name, poem, story, image_path) VALUES 
(1, '妖怪名称', '相关诗句', '故事描述', '图片路径');
```

### 自定义样式

可以修改 `src/main/resources/static/css/style.css` 文件来自定义界面样式。

### 扩展功能

- 可以在 `MonsterController` 中添加新的API接口
- 可以在 `MonsterService` 中添加新的业务逻辑
- 可以在前端 `app.js` 中添加新的交互功能

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 确认数据库连接配置是否正确
   - 检查用户名密码是否正确

2. **端口占用**
   - 默认端口8080被占用时，可以在 `application.yml` 中修改端口号

3. **图片显示问题**
   - 确保图片路径正确
   - 检查图片文件是否存在

## 许可证

本项目仅用于学习和展示目的。

## 致谢

感谢《黑神话：悟空》游戏提供的精彩内容和灵感。
